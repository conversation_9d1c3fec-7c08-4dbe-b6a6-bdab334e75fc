import java.io.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

// Custom Exception Class
class PayrollException extends Exception {
    private String errorCode;

    public PayrollException(String message) {
        super(message);
        this.errorCode = "PAYROLL_ERROR";
    }

    public PayrollException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode() { return errorCode; }
    public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
}

// Interface for Payroll Calculations
interface PayrollCalculator {
    double calculateWeeklyGrossPay() throws PayrollException;
    double calculateDeductions() throws PayrollException;
    double calculateNetPay() throws PayrollException;
    double calculateOvertimePay(double overtimeHours) throws PayrollException;
    double calculateHolidayPay(int holidayDays, boolean isDoublePayHoliday) throws PayrollException;
}

// Time Record Class
class TimeRecord {
    private String date;
    private String timeIn1, timeOut1, timeIn2, timeOut2;
    private double lateMinutes, undertimeMinutes, overtimeHours;
    private boolean isHoliday, isDoublePayHoliday;

    // Constructor Overloading
    public TimeRecord(String date) {
        this.date = date;
        this.lateMinutes = 0;
        this.undertimeMinutes = 0;
        this.overtimeHours = 0;
        this.isHoliday = false;
        this.isDoublePayHoliday = false;
    }

    public TimeRecord(String date, String timeIn1, String timeOut1) {
        this(date);
        this.timeIn1 = timeIn1;
        this.timeOut1 = timeOut1;
    }

    // Accessor and Mutator Methods
    public String getDate() { return date; }
    public void setDate(String date) { this.date = date; }

    public String getTimeIn1() { return timeIn1; }
    public void setTimeIn1(String timeIn1) { this.timeIn1 = timeIn1; }

    public String getTimeOut1() { return timeOut1; }
    public void setTimeOut1(String timeOut1) { this.timeOut1 = timeOut1; }

    public String getTimeIn2() { return timeIn2; }
    public void setTimeIn2(String timeIn2) { this.timeIn2 = timeIn2; }

    public String getTimeOut2() { return timeOut2; }
    public void setTimeOut2(String timeOut2) { this.timeOut2 = timeOut2; }

    public double getLateMinutes() { return lateMinutes; }
    public void setLateMinutes(double lateMinutes) { this.lateMinutes = lateMinutes; }

    public double getUndertimeMinutes() { return undertimeMinutes; }
    public void setUndertimeMinutes(double undertimeMinutes) { this.undertimeMinutes = undertimeMinutes; }

    public double getOvertimeHours() { return overtimeHours; }
    public void setOvertimeHours(double overtimeHours) { this.overtimeHours = overtimeHours; }

    public boolean isHoliday() { return isHoliday; }
    public void setHoliday(boolean holiday) { isHoliday = holiday; }

    public boolean isDoublePayHoliday() { return isDoublePayHoliday; }
    public void setDoublePayHoliday(boolean doublePayHoliday) { isDoublePayHoliday = doublePayHoliday; }
}

// Abstract Employee Class
abstract class Employee implements PayrollCalculator {
    // Instance Variables
    protected String employeeId;
    protected String name;
    protected String employmentType;
    protected String position;
    protected double monthlySalary;
    protected double weeklyIncentive;
    protected double ecola;
    protected List<TimeRecord> timeRecords;
    protected double cashAdvance;
    protected double loans;
    protected LocalDate salaryEffectiveDate;

    // Constructor Overloading
    public Employee() {
        this.timeRecords = new ArrayList<>();
        this.ecola = 300.0; // Default ECOLA
        this.salaryEffectiveDate = LocalDate.now();
    }

    public Employee(String employeeId, String name) {
        this();
        this.employeeId = employeeId;
        this.name = name;
    }

    public Employee(String employeeId, String name, String employmentType, String position) {
        this(employeeId, name);
        this.employmentType = employmentType;
        this.position = position;
        setDefaultSalaryAndIncentive();
    }

    // Abstract method to be implemented by subclasses
    public abstract void setDefaultSalaryAndIncentive();

    // Method Overriding example
    @Override
    public String toString() {
        return "Employee{" +
                "ID='" + employeeId + '\'' +
                ", Name='" + name + '\'' +
                ", Type='" + employmentType + '\'' +
                ", Position='" + position + '\'' +
                ", Monthly Salary=" + monthlySalary +
                '}';
    }

    // Accessor and Mutator Methods
    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getEmploymentType() { return employmentType; }
    public void setEmploymentType(String employmentType) { this.employmentType = employmentType; }

    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }

    public double getMonthlySalary() { return monthlySalary; }
    public void setMonthlySalary(double monthlySalary) { this.monthlySalary = monthlySalary; }

    public double getWeeklyIncentive() { return weeklyIncentive; }
    public void setWeeklyIncentive(double weeklyIncentive) { this.weeklyIncentive = weeklyIncentive; }

    public double getEcola() { return ecola; }
    public void setEcola(double ecola) { this.ecola = ecola; }

    public List<TimeRecord> getTimeRecords() { return timeRecords; }
    public void setTimeRecords(List<TimeRecord> timeRecords) { this.timeRecords = timeRecords; }

    public double getCashAdvance() { return cashAdvance; }
    public void setCashAdvance(double cashAdvance) { this.cashAdvance = cashAdvance; }

    public double getLoans() { return loans; }
    public void setLoans(double loans) { this.loans = loans; }

    public LocalDate getSalaryEffectiveDate() { return salaryEffectiveDate; }
    public void setSalaryEffectiveDate(LocalDate salaryEffectiveDate) { this.salaryEffectiveDate = salaryEffectiveDate; }

    // Method Overloading for adding time records
    public void addTimeRecord(String date) {
        timeRecords.add(new TimeRecord(date));
    }

    public void addTimeRecord(String date, String timeIn, String timeOut) {
        timeRecords.add(new TimeRecord(date, timeIn, timeOut));
    }

    public void addTimeRecord(TimeRecord record) {
        timeRecords.add(record);
    }

    // Implementation of PayrollCalculator interface
    @Override
    public double calculateWeeklyGrossPay() throws PayrollException {
        try {
            double weeklyBasePay = monthlySalary / 4.0; // Convert monthly to weekly
            return weeklyBasePay + weeklyIncentive + ecola;
        } catch (Exception e) {
            throw new PayrollException("Error calculating weekly gross pay: " + e.getMessage());
        }
    }

    @Override
    public double calculateDeductions() throws PayrollException {
        try {
            double sss = monthlySalary * 0.045; // 4.5% SSS
            double pagibig = monthlySalary * 0.02; // 2% Pag-ibig
            double philhealth = monthlySalary * 0.025; // 2.5% PhilHealth

            double weeklyDeductions = (sss + pagibig + philhealth) / 4.0; // Convert to weekly

            // Add late and undertime deductions
            double timeDeductions = 0;
            for (TimeRecord record : timeRecords) {
                timeDeductions += applyTimeDeductions(record.getLateMinutes(), record.getUndertimeMinutes());
            }

            return weeklyDeductions + timeDeductions + (cashAdvance / 4.0) + (loans / 4.0);
        } catch (Exception e) {
            throw new PayrollException("Error calculating deductions: " + e.getMessage());
        }
    }

    @Override
    public double calculateNetPay() throws PayrollException {
        try {
            double grossPay = calculateWeeklyGrossPay();
            double deductions = calculateDeductions();

            // Add overtime and holiday pay
            double overtimePay = 0;
            double holidayPay = 0;
            int holidayDays = 0;
            int doublePayHolidays = 0;

            for (TimeRecord record : timeRecords) {
                overtimePay += calculateOvertimePay(record.getOvertimeHours());
                if (record.isHoliday()) {
                    holidayDays++;
                    if (record.isDoublePayHoliday()) {
                        doublePayHolidays++;
                    }
                }
            }

            holidayPay += calculateHolidayPay(holidayDays - doublePayHolidays, false);
            holidayPay += calculateHolidayPay(doublePayHolidays, true);

            return grossPay + overtimePay + holidayPay - deductions;
        } catch (Exception e) {
            throw new PayrollException("Error calculating net pay: " + e.getMessage());
        }
    }

    @Override
    public double calculateOvertimePay(double overtimeHours) throws PayrollException {
        try {
            double hourlyRate = (monthlySalary / 4.0) / 40.0; // Weekly salary / 40 hours
            return overtimeHours * hourlyRate * 1.25; // 125% for overtime
        } catch (Exception e) {
            throw new PayrollException("Error calculating overtime pay: " + e.getMessage());
        }
    }

    @Override
    public double calculateHolidayPay(int holidayDays, boolean isDoublePayHoliday) throws PayrollException {
        try {
            double dailyRate = (monthlySalary / 4.0) / 5.0; // Weekly salary / 5 days
            if (isDoublePayHoliday) {
                return holidayDays * dailyRate * 2.0; // Double pay
            } else {
                return holidayDays * dailyRate * 1.3; // 130% for regular holidays
            }
        } catch (Exception e) {
            throw new PayrollException("Error calculating holiday pay: " + e.getMessage());
        }
    }

    public double applyTimeDeductions(double lateMinutes, double undertimeMinutes) throws PayrollException {
        try {
            double hourlyRate = (monthlySalary / 4.0) / 40.0;
            double minuteRate = hourlyRate / 60.0;
            return (lateMinutes + undertimeMinutes) * minuteRate;
        } catch (Exception e) {
            throw new PayrollException("Error applying time deductions: " + e.getMessage());
        }
    }
}

// Permanent Employee Class (Inheritance)
class PermanentEmployee extends Employee {

    public PermanentEmployee() {
        super();
        this.employmentType = "Permanent";
    }

    public PermanentEmployee(String employeeId, String name, String position) {
        super(employeeId, name, "Permanent", position);
    }

    @Override
    public void setDefaultSalaryAndIncentive() {
        switch (position.toLowerCase()) {
            case "regular":
                this.monthlySalary = 32000.0;
                this.weeklyIncentive = 0.0;
                break;
            case "manager":
                this.monthlySalary = 50000.0;
                this.weeklyIncentive = 700.0;
                break;
            case "supervisor":
                this.monthlySalary = 43000.0;
                this.weeklyIncentive = 300.0;
                break;
            default:
                this.monthlySalary = 32000.0;
                this.weeklyIncentive = 0.0;
        }
    }
}

// Temporary Employee Class (Inheritance)
class TemporaryEmployee extends Employee {

    public TemporaryEmployee() {
        super();
        this.employmentType = "Temporary";
    }

    public TemporaryEmployee(String employeeId, String name) {
        super(employeeId, name, "Temporary", "Temp");
    }

    @Override
    public void setDefaultSalaryAndIncentive() {
        this.monthlySalary = 30000.0;
        this.weeklyIncentive = 0.0;
    }
}

// Contract Employee Classes (Inheritance)
class ContractOfServiceEmployee extends Employee {

    public ContractOfServiceEmployee() {
        super();
        this.employmentType = "Contract of Service";
    }

    public ContractOfServiceEmployee(String employeeId, String name) {
        super(employeeId, name, "Contract of Service", "COS");
    }

    @Override
    public void setDefaultSalaryAndIncentive() {
        this.monthlySalary = 22000.0;
        this.weeklyIncentive = 0.0;
    }
}

class JobOrderEmployee extends Employee {

    public JobOrderEmployee() {
        super();
        this.employmentType = "Job Order";
    }

    public JobOrderEmployee(String employeeId, String name) {
        super(employeeId, name, "Job Order", "JO");
    }

    @Override
    public void setDefaultSalaryAndIncentive() {
        this.monthlySalary = 15000.0;
        this.weeklyIncentive = 0.0;
    }
}

// PaySlip Class
class PaySlip {
    private Employee employee;
    private String payPeriod;
    private double grossPay;
    private double deductions;
    private double netPay;
    private double overtimePay;
    private double holidayPay;
    private LocalDate generatedDate;

    public PaySlip(Employee employee, String payPeriod) throws PayrollException {
        this.employee = employee;
        this.payPeriod = payPeriod;
        this.generatedDate = LocalDate.now();
        calculatePayroll();
    }

    private void calculatePayroll() throws PayrollException {
        this.grossPay = employee.calculateWeeklyGrossPay();
        this.deductions = employee.calculateDeductions();
        this.netPay = employee.calculateNetPay();

        // Calculate overtime and holiday pay separately for display
        this.overtimePay = 0;
        this.holidayPay = 0;
        for (TimeRecord record : employee.getTimeRecords()) {
            this.overtimePay += employee.calculateOvertimePay(record.getOvertimeHours());
            if (record.isHoliday()) {
                this.holidayPay += employee.calculateHolidayPay(1, record.isDoublePayHoliday());
            }
        }
    }

    public void displayPaySlip() {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           WEEKLY PAY SLIP");
        System.out.println("=".repeat(50));
        System.out.println("Employee ID: " + employee.getEmployeeId());
        System.out.println("Name: " + employee.getName());
        System.out.println("Position: " + employee.getPosition());
        System.out.println("Employment Type: " + employee.getEmploymentType());
        System.out.println("Pay Period: " + payPeriod);
        System.out.println("Generated: " + generatedDate.format(DateTimeFormatter.ofPattern("MMM dd, yyyy")));
        System.out.println("-".repeat(50));

        System.out.printf("Monthly Salary: Php %,.2f%n", employee.getMonthlySalary());
        System.out.printf("Weekly Base Pay: Php %,.2f%n", employee.getMonthlySalary() / 4.0);
        System.out.printf("Weekly Incentive: Php %,.2f%n", employee.getWeeklyIncentive());
        System.out.printf("ECOLA: Php %,.2f%n", employee.getEcola());
        if (overtimePay > 0) {
            System.out.printf("Overtime Pay: Php %,.2f%n", overtimePay);
        }
        if (holidayPay > 0) {
            System.out.printf("Holiday Pay: Php %,.2f%n", holidayPay);
        }
        System.out.println("-".repeat(50));
        System.out.printf("GROSS PAY: Php %,.2f%n", grossPay + overtimePay + holidayPay);

        System.out.println("\nDEDUCTIONS:");
        double sss = employee.getMonthlySalary() * 0.045 / 4.0;
        double pagibig = employee.getMonthlySalary() * 0.02 / 4.0;
        double philhealth = employee.getMonthlySalary() * 0.025 / 4.0;

        System.out.printf("SSS: Php %,.2f%n", sss);
        System.out.printf("Pag-ibig: Php %,.2f%n", pagibig);
        System.out.printf("PhilHealth: Php %,.2f%n", philhealth);

        if (employee.getCashAdvance() > 0) {
            System.out.printf("Cash Advance: Php %,.2f%n", employee.getCashAdvance() / 4.0);
        }
        if (employee.getLoans() > 0) {
            System.out.printf("Loans: Php %,.2f%n", employee.getLoans() / 4.0);
        }

        System.out.println("-".repeat(50));
        System.out.printf("Total Deductions: Php %,.2f%n", deductions);
        System.out.printf("NET PAY: Php %,.2f%n", netPay);
        System.out.println("=".repeat(50));
    }

    // Accessor methods
    public Employee getEmployee() { return employee; }
    public String getPayPeriod() { return payPeriod; }
    public double getGrossPay() { return grossPay; }
    public double getDeductions() { return deductions; }
    public double getNetPay() { return netPay; }
    public LocalDate getGeneratedDate() { return generatedDate; }
}

// File Management and Main System Class
class PayrollManager {
    private List<Employee> employees;
    private List<PaySlip> payrollHistory;
    private Scanner scanner;

    public PayrollManager() {
        this.employees = new ArrayList<>();
        this.payrollHistory = new ArrayList<>();
        this.scanner = new Scanner(System.in);
        loadEmployeesFromFile();
    }

    // File Operations
    public void saveEmployeesToFile() {
        try (PrintWriter writer = new PrintWriter(new FileWriter("employees.txt"))) {
            for (Employee emp : employees) {
                writer.println(emp.getEmployeeId() + "," + emp.getName() + "," +
                             emp.getEmploymentType() + "," + emp.getPosition() + "," +
                             emp.getMonthlySalary() + "," + emp.getWeeklyIncentive() + "," +
                             emp.getEcola() + "," + emp.getCashAdvance() + "," + emp.getLoans());
            }
            System.out.println("Employee data saved successfully!");
        } catch (IOException e) {
            System.out.println("Error saving employee data: " + e.getMessage());
        }
    }

    public void loadEmployeesFromFile() {
        try (BufferedReader reader = new BufferedReader(new FileReader("employees.txt"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length >= 9) {
                    Employee emp = createEmployee(parts[2], parts[0], parts[1], parts[3]);
                    emp.setMonthlySalary(Double.parseDouble(parts[4]));
                    emp.setWeeklyIncentive(Double.parseDouble(parts[5]));
                    emp.setEcola(Double.parseDouble(parts[6]));
                    emp.setCashAdvance(Double.parseDouble(parts[7]));
                    emp.setLoans(Double.parseDouble(parts[8]));
                    employees.add(emp);
                }
            }
            System.out.println("Employee data loaded successfully!");
        } catch (FileNotFoundException e) {
            System.out.println("No existing employee file found. Starting fresh.");
        } catch (IOException e) {
            System.out.println("Error loading employee data: " + e.getMessage());
        }
    }

    // Method Overloading for creating employees
    public Employee createEmployee(String employmentType, String id, String name) {
        return createEmployee(employmentType, id, name, "Regular");
    }

    public Employee createEmployee(String employmentType, String id, String name, String position) {
        Employee employee;
        switch (employmentType.toLowerCase()) {
            case "permanent":
                employee = new PermanentEmployee(id, name, position);
                break;
            case "temporary":
                employee = new TemporaryEmployee(id, name);
                break;
            case "contract of service":
                employee = new ContractOfServiceEmployee(id, name);
                break;
            case "job order":
                employee = new JobOrderEmployee(id, name);
                break;
            default:
                employee = new PermanentEmployee(id, name, "Regular");
        }
        return employee;
    }

    public void addEmployee() {
        try {
            System.out.print("Enter Employee ID: ");
            String id = scanner.nextLine();

            // Check if employee already exists
            if (findEmployeeById(id) != null) {
                throw new PayrollException("Employee with ID " + id + " already exists!");
            }

            System.out.print("Enter Employee Name: ");
            String name = scanner.nextLine();

            System.out.println("Select Employment Type:");
            System.out.println("1. Permanent");
            System.out.println("2. Temporary");
            System.out.println("3. Contract of Service");
            System.out.println("4. Job Order");
            System.out.print("Choice: ");
            int choice = Integer.parseInt(scanner.nextLine());

            String employmentType;
            String position = "Regular";

            switch (choice) {
                case 1:
                    employmentType = "Permanent";
                    System.out.println("Select Position:");
                    System.out.println("1. Regular");
                    System.out.println("2. Manager");
                    System.out.println("3. Supervisor");
                    System.out.print("Choice: ");
                    int posChoice = Integer.parseInt(scanner.nextLine());
                    switch (posChoice) {
                        case 1: position = "Regular"; break;
                        case 2: position = "Manager"; break;
                        case 3: position = "Supervisor"; break;
                        default: position = "Regular";
                    }
                    break;
                case 2: employmentType = "Temporary"; position = "Temp"; break;
                case 3: employmentType = "Contract of Service"; position = "COS"; break;
                case 4: employmentType = "Job Order"; position = "JO"; break;
                default: employmentType = "Permanent";
            }

            Employee employee = createEmployee(employmentType, id, name, position);
            employees.add(employee);

            System.out.println("Employee added successfully!");
            System.out.println("Monthly Salary: Php " + String.format("%,.2f", employee.getMonthlySalary()));
            System.out.println("Weekly Incentive: Php " + String.format("%,.2f", employee.getWeeklyIncentive()));

        } catch (PayrollException e) {
            System.out.println("Error: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("Invalid input. Please try again.");
        }
    }

    public Employee findEmployeeById(String id) {
        for (Employee emp : employees) {
            if (emp.getEmployeeId().equals(id)) {
                return emp;
            }
        }
        return null;
    }

    public void generatePayroll() {
        try {
            System.out.print("Enter Employee ID: ");
            String id = scanner.nextLine();

            Employee employee = findEmployeeById(id);
            if (employee == null) {
                throw new PayrollException("Employee not found!");
            }

            System.out.print("Enter Pay Period (e.g., Week 1 - January 2024): ");
            String payPeriod = scanner.nextLine();

            // Add time records for the week
            addTimeRecordsForWeek(employee);

            PaySlip paySlip = new PaySlip(employee, payPeriod);
            payrollHistory.add(paySlip);

            paySlip.displayPaySlip();

        } catch (PayrollException e) {
            System.out.println("Error: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("Error generating payroll: " + e.getMessage());
        }
    }

    private void addTimeRecordsForWeek(Employee employee) {
        employee.getTimeRecords().clear(); // Clear previous records

        String[] days = {"Monday", "Tuesday", "Wednesday", "Thursday", "Friday"};

        for (String day : days) {
            TimeRecord record = new TimeRecord(day);

            System.out.println("\n--- " + day + " ---");
            System.out.print("Late minutes (0 if none): ");
            record.setLateMinutes(Double.parseDouble(scanner.nextLine()));

            System.out.print("Undertime minutes (0 if none): ");
            record.setUndertimeMinutes(Double.parseDouble(scanner.nextLine()));

            System.out.print("Overtime hours (0 if none): ");
            record.setOvertimeHours(Double.parseDouble(scanner.nextLine()));

            System.out.print("Is this a holiday? (y/n): ");
            boolean isHoliday = scanner.nextLine().toLowerCase().startsWith("y");
            record.setHoliday(isHoliday);

            if (isHoliday) {
                System.out.print("Is this a double pay holiday? (y/n): ");
                record.setDoublePayHoliday(scanner.nextLine().toLowerCase().startsWith("y"));
            }

            employee.addTimeRecord(record);
        }

        // Add cash advance and loans
        System.out.print("\nCash advance for this week (0 if none): ");
        employee.setCashAdvance(Double.parseDouble(scanner.nextLine()));

        System.out.print("Loan deduction for this week (0 if none): ");
        employee.setLoans(Double.parseDouble(scanner.nextLine()));
    }

    public void viewPayrollHistory() {
        if (payrollHistory.isEmpty()) {
            System.out.println("No payroll history found.");
            return;
        }

        System.out.print("Enter Employee ID: ");
        String id = scanner.nextLine();

        boolean found = false;
        for (PaySlip paySlip : payrollHistory) {
            if (paySlip.getEmployee().getEmployeeId().equals(id)) {
                paySlip.displayPaySlip();
                found = true;
            }
        }

        if (!found) {
            System.out.println("No payroll history found for employee ID: " + id);
        }
    }

    public void updateSalaryAndBenefits() {
        try {
            System.out.print("Enter Employee ID: ");
            String id = scanner.nextLine();

            Employee employee = findEmployeeById(id);
            if (employee == null) {
                throw new PayrollException("Employee not found!");
            }

            System.out.println("Current Details:");
            System.out.println("Monthly Salary: Php " + String.format("%,.2f", employee.getMonthlySalary()));
            System.out.println("Weekly Incentive: Php " + String.format("%,.2f", employee.getWeeklyIncentive()));
            System.out.println("ECOLA: Php " + String.format("%,.2f", employee.getEcola()));

            System.out.print("\nEnter new monthly salary (or press Enter to keep current): ");
            String salaryInput = scanner.nextLine();
            if (!salaryInput.trim().isEmpty()) {
                employee.setMonthlySalary(Double.parseDouble(salaryInput));
            }

            System.out.print("Enter new weekly incentive (or press Enter to keep current): ");
            String incentiveInput = scanner.nextLine();
            if (!incentiveInput.trim().isEmpty()) {
                employee.setWeeklyIncentive(Double.parseDouble(incentiveInput));
            }

            System.out.print("Enter new ECOLA (or press Enter to keep current): ");
            String ecolaInput = scanner.nextLine();
            if (!ecolaInput.trim().isEmpty()) {
                employee.setEcola(Double.parseDouble(ecolaInput));
            }

            System.out.print("Enter effective date (YYYY-MM-DD) or press Enter for today: ");
            String dateInput = scanner.nextLine();
            if (!dateInput.trim().isEmpty()) {
                employee.setSalaryEffectiveDate(LocalDate.parse(dateInput));
            } else {
                employee.setSalaryEffectiveDate(LocalDate.now());
            }

            System.out.println("Salary and benefits updated successfully!");
            System.out.println("Effective Date: " + employee.getSalaryEffectiveDate());

        } catch (PayrollException e) {
            System.out.println("Error: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("Invalid input. Please try again.");
        }
    }

    public void displayAllEmployees() {
        if (employees.isEmpty()) {
            System.out.println("No employees found.");
            return;
        }

        System.out.println("\n" + "=".repeat(80));
        System.out.println("                           ALL EMPLOYEES");
        System.out.println("=".repeat(80));
        System.out.printf("%-10s %-20s %-15s %-12s %-12s%n",
                         "ID", "Name", "Type", "Position", "Monthly Salary");
        System.out.println("-".repeat(80));

        for (Employee emp : employees) {
            System.out.printf("%-10s %-20s %-15s %-12s Php %,.2f%n",
                             emp.getEmployeeId(),
                             emp.getName(),
                             emp.getEmploymentType(),
                             emp.getPosition(),
                             emp.getMonthlySalary());
        }
        System.out.println("=".repeat(80));
    }

    public void showMenu() {
        while (true) {
            System.out.println("\n" + "=".repeat(50));
            System.out.println("           PAYROLL MANAGEMENT SYSTEM");
            System.out.println("=".repeat(50));
            System.out.println("1. Add New Employee");
            System.out.println("2. Generate Weekly Payroll");
            System.out.println("3. View Payroll History");
            System.out.println("4. Update Salary & Benefits");
            System.out.println("5. Display All Employees");
            System.out.println("6. Save Data");
            System.out.println("7. Exit");
            System.out.print("Choose an option: ");

            try {
                int choice = Integer.parseInt(scanner.nextLine());

                switch (choice) {
                    case 1: addEmployee(); break;
                    case 2: generatePayroll(); break;
                    case 3: viewPayrollHistory(); break;
                    case 4: updateSalaryAndBenefits(); break;
                    case 5: displayAllEmployees(); break;
                    case 6: saveEmployeesToFile(); break;
                    case 7:
                        saveEmployeesToFile();
                        System.out.println("Thank you for using the Payroll System!");
                        return;
                    default:
                        System.out.println("Invalid choice. Please try again.");
                }
            } catch (NumberFormatException e) {
                System.out.println("Please enter a valid number.");
            }
        }
    }
}

// Main Class
public class PayrollSystem {
    public static void main(String[] args) {
        System.out.println("Welcome to the Comprehensive Payroll Management System!");
        System.out.println("This system supports:");
        System.out.println("- Multiple employee types (Permanent, Temporary, Contract, Job Order)");
        System.out.println("- Weekly payroll generation with detailed pay slips");
        System.out.println("- Overtime and holiday pay calculations");
        System.out.println("- File-based data persistence");
        System.out.println("- Salary and benefits management");

        PayrollManager manager = new PayrollManager();
        manager.showMenu();
    }
}
