import java.util.Scanner;

public class Employee1 {
    String name, ID; 
    Float salary, days;

    
    Float calculateProduct() {
        return salary * days; 
    }

    
    public static void main(String[] args) {
        Scanner data = new Scanner(System.in);
        Employee1 person = new Employee1(); 

       
        System.out.println("Enter your Name:");
        person.name = data.nextLine();

        System.out.println("Enter ID Number:");
        person.ID = data.nextLine();

        System.out.println("Enter Salary Amount per Day:");
        person.salary = data.nextFloat();

        System.out.println("Enter Number of Days:");
        person.days = data.nextFloat();

        // Display employee details
        System.out.println();
        System.out.println("Employee Name: " + person.name);
        System.out.println("ID Number: " + person.ID);
        System.out.println("Salary per Day: Php" + person.salary);
        System.out.println("Total Earnings: Php" + person.calculateProduct()); 

        data.close();
    }
}