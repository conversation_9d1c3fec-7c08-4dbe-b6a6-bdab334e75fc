public class dreamhouse {
    public static void main(String[] args) {
        System.out.println("        /\\\\\\\\\\\\\\\\\\\\\\\\       ");
        System.out.println("       /  \\\\\\\\\\\\\\\\\\\\\\\\     ");
        System.out.println("      /____\\\\\\\\\\\\\\\\\\\\\\\\      ");
        System.out.println("     |      |                     |    ");
        System.out.println("     |  []  |     []      []      |   ");
        System.out.println("     |      |                     |     ");
        System.out.println("     |______|_____________________| ");
        System.out.println("     |  __  |      __      __     |     ");
        System.out.println("     | |  | |     |  |    |  |    |");
        System.out.println("     | |__| |     |__|    |__|    | ");
        System.out.println("     |______|_____________________|     ");
        System.out.println("     |  __  |           ______    |");
        System.out.println("     | |  | |   __     |      |   |   ");
        System.out.println("     | |__| |  |  |    |______|   |");
        System.out.println("     |______|__|__|__************ |");
        System.out.println("                ||        ");
        System.out.println("                ||        ");
        System.out.println("                ||        ");
        System.out.println("                ||        ");
        System.out.println("                ||        ");
        System.out.println("                ||        ");
        
    }
    
}
