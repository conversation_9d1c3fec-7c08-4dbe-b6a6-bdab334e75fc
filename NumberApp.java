import java.util.Scanner; // Import Scanner class para sa user input

public class NumberApp {
    private String binNumber; // Variable para sa binary number nga string

    // Setter method para i-assign ang binary number gikan sa user input
    public void setBinNumber(String binNumber) {
        this.binNumber = binNumber;
    }

    // Method para manually convert ang binary number to decimal
    public int getDecNumber() {
        int decimal = 0; // Variable para sa decimal equivalent
        int power = 1; // 2^0 = 1, initial value para sa power of 2
        
        // Loop gikan sa pinakawalay bili (rightmost) padulong sa leftmost digit
        for (int i = binNumber.length() - 1; i >= 0; i--) {
            if (binNumber.charAt(i) == '1') { // Kung '1' ang character
                decimal += power; // I-add ang equivalent value sa decimal
            }
            power *= 2; // Multiply by 2 para sa sunod nga power of 2
        }
        return decimal; // I-return ang decimal equivalent
    }

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in); // Scanner object para makakuha ug input
        System.out.print("Enter binary number: "); // Prompt para sa user input
        String binaryInput = scanner.nextLine(); // Basaha ang user input nga binary number

        NumberApp converter = new NumberApp(); // Create object sa NumberApp class
        converter.setBinNumber(binaryInput); // Tawagon ang setter method para i-store ang binary number

        int decimal = converter.getDecNumber(); // Tawagon ang method para makuha ang decimal equivalent
        String octal = ""; // Empty string para sa octal conversion
        int num = decimal; // I-assign ang decimal number para sa manual octal conversion

        // Manual conversion sa decimal number padulong octal
        while (num > 0) {
            octal = (num % 8) + octal; // Kuhaa ang remainder sa division by 8 ug i-concatenate
            num /= 8; // I-divide ang number by 8 para makuha ang next digit
        }

        // Display sa results
        System.out.println("Decimal number is: " + decimal); // Ipakita ang decimal equivalent
        System.out.println("Octal number is: " + (octal.isEmpty() ? "0" : octal)); // Ipakita ang octal equivalent

        scanner.close(); // I-close ang scanner object para malikayan ang memory leaks
    }
}
