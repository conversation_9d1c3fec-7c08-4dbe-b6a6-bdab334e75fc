/**
 * Interface for payroll calculations
 * Demonstrates interface implementation
 */
public interface PayrollCalculator {
    
    // Calculate weekly gross pay
    double calculateWeeklyGrossPay() throws PayrollException;
    
    // Calculate deductions
    double calculateDeductions() throws PayrollException;
    
    // Calculate net pay
    double calculateNetPay() throws PayrollException;
    
    // Calculate overtime pay (for additional points)
    double calculateOvertimePay(double overtimeHours) throws PayrollException;
    
    // Calculate holiday pay (for additional points)
    double calculateHolidayPay(int holidayDays, boolean isDoublePayHoliday) throws PayrollException;
    
    // Apply late/undertime deductions
    double applyTimeDeductions(double lateMinutes, double undertimeMinutes) throws PayrollException;
}
