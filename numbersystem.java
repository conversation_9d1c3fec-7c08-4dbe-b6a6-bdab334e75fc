import java.util.Scanner; // Import Scanner class for user input

public class numbersystem {  // Main class
    
    // Method to convert input number to decimal based on its base
    public static int convertToDecimal(String number, char base) {
        int decimalValue = 0; // Initialize decimal value
        int power = 0; // Power counter for base conversion
        
        switch (base) { // Switch case to handle different bases
            case 'b': // Binary conversion
                for (int i = number.length() - 1; i >= 0; i--) { // Iterate through digits
                    char digit = number.charAt(i); // Get current digit
                    if (digit != '0' && digit != '1') { // Validate binary digit
                        throw new IllegalArgumentException("Invalid binary number");
                    }
                    decimalValue += (digit - '0') * Math.pow(2, power); // Convert to decimal
                    power++; // Increment power
                }
                break;
            case 'o': // Octal conversion
                for (int i = number.length() - 1; i >= 0; i--) { // Iterate through digits
                    char digit = number.charAt(i); // Get current digit
                    if (digit < '0' || digit > '7') { // Validate octal digit
                        throw new IllegalArgumentException("Invalid octal number");
                    }
                    decimalValue += (digit - '0') * Math.pow(8, power); // Convert to decimal
                    power++; // Increment power
                }
                break;
            case 'h': // Hexadecimal conversion
                for (int i = number.length() - 1; i >= 0; i--) { // Iterate through digits
                    char digit = number.charAt(i); // Get current digit
                    if (!Character.isDigit(digit) && (digit < 'A' || digit > 'F')) { // Validate hex digit
                        throw new IllegalArgumentException("Invalid hexadecimal number");
                    }
                    int value = (digit >= '0' && digit <= '9') ? digit - '0' : digit - 'A' + 10; // Convert hex to decimal
                    decimalValue += value * Math.pow(16, power); // Convert to decimal
                    power++; // Increment power
                }
                break;
            case 'd': // Decimal conversion
                for (int i = 0; i < number.length(); i++) { // Iterate through digits
                    char digit = number.charAt(i); // Get current digit
                    if (!Character.isDigit(digit)) { // Validate decimal digit
                        throw new IllegalArgumentException("Invalid decimal number");
                    }
                    decimalValue = decimalValue * 10 + (digit - '0'); // Convert to decimal
                }
                break;
            default:
                throw new IllegalArgumentException("Invalid base selection"); // Handle invalid base
        }
        return decimalValue; // Return converted decimal value
    }
    
    public static void main(String[] args) { // Main method
        Scanner scanner = new Scanner(System.in); // Create scanner object for input
        
        try {
            System.out.print("Enter number1: "); // Prompt user for first number
            String num1 = scanner.next(); // Read first number
            System.out.println("Choose the number system of number1:"); // Display menu
            System.out.println("[d]  Decimal number");
            System.out.println("[b]  Binary number");
            System.out.println("[o]  Octal number");
            System.out.println("[h]  Hexadecimal number");
            System.out.print("Select: "); // Prompt user for base
            char base1 = scanner.next().charAt(0); // Read base of first number
            
            System.out.print("Enter number2: "); // Prompt user for second number
            String num2 = scanner.next(); // Read second number
            System.out.println("Choose the number system of number2:"); // Display menu
            System.out.println("[d]  Decimal number");
            System.out.println("[b]  Binary number");
            System.out.println("[o]  Octal number");
            System.out.println("[h]  Hexadecimal number");
            System.out.print("Select: "); // Prompt user for base
            char base2 = scanner.next().charAt(0); // Read base of second number
            
            int decimal1 = convertToDecimal(num1, base1); // Convert first number to decimal
            int decimal2 = convertToDecimal(num2, base2); // Convert second number to decimal
            
            System.out.println("Choose the operation to perform in number1 and number2"); // Display operations
            System.out.println("[a] Addition");
            System.out.println("[s] Subtraction");
            System.out.println("[m] Multiplication");
            System.out.println("[d] Division");
            System.out.print("Select: "); // Prompt user for operation
            char operation = scanner.next().charAt(0); // Read operation selection
            
            int result = 0; // Initialize result variable
            switch (operation) { // Perform the selected operation
                case 'a': result = decimal1 + decimal2; break; // Addition
                case 's': result = decimal1 - decimal2; break; // Subtraction
                case 'm': result = decimal1 * decimal2; break; // Multiplication
                case 'd': 
                    if (decimal2 == 0) { // Check for division by zero
                        System.out.println("Error: Division by zero is not allowed."); // Display error message
                        return; // Exit program
                    }
                    result = decimal1 / decimal2; // Perform division
                    break;
                default:
                    System.out.println("Invalid operation selection."); // Handle invalid operation
                    return; // Exit program
            }
            
            System.out.println("The result of " + num1 + " (base " + base1 + ") and " + num2 + " (base " + base2 + ") is: " + result + " (base 10)"); // Display result
        } catch (IllegalArgumentException e) { // Catch invalid input errors
            System.out.println("Error: " + e.getMessage()); // Display error message
        } finally {
            scanner.close(); // Close scanner to prevent memory leaks
        }
    }
}
