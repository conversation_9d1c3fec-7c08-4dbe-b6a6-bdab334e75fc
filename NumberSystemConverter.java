import java.util.Scanner;

public class NumberSystemConverter {

    // Convert binary string to decimal
    public static int binaryToDecimal(String number) {
        int decimal = 0;
        int power = 0;

        for (int i = number.length() - 1; i >= 0; i--) {
            char digit = number.charAt(i);
            if (digit != '0' && digit != '1') {
                throw new IllegalArgumentException("Invalid binary digit: " + digit);
            }
            decimal += (digit - '0') * Math.pow(2, power);
            power++;
        }
        return decimal;
    }

    // Convert octal string to decimal
    public static int octalToDecimal(String number) {
        int decimal = 0;
        int power = 0;

        for (int i = number.length() - 1; i >= 0; i--) {
            char digit = number.charAt(i);
            if (digit < '0' || digit > '7') {
                throw new IllegalArgumentException("Invalid octal digit: " + digit);
            }
            decimal += (digit - '0') * Math.pow(8, power);
            power++;
        }
        return decimal;
    }

    // Convert hexadecimal string to decimal
    public static int hexToDecimal(String number) {
        int decimal = 0;
        int power = 0;

        for (int i = number.length() - 1; i >= 0; i--) {
            char digit = Character.toUpperCase(number.charAt(i));
            int value;
            if (digit >= '0' && digit <= '9') {
                value = digit - '0';
            } else if (digit >= 'A' && digit <= 'F') {
                value = digit - 'A' + 10;
            } else {
                throw new IllegalArgumentException("Invalid hex digit: " + digit);
            }
            decimal += value * Math.pow(16, power);
            power++;
        }
        return decimal;
    }

    // Convert decimal string to integer
    public static int decimalToDecimal(String number) {
        int decimal = 0;

        for (int i = 0; i < number.length(); i++) {
            char digit = number.charAt(i);
            if (!Character.isDigit(digit)) {
                throw new IllegalArgumentException("Invalid decimal digit: " + digit);
            }
            decimal = decimal * 10 + (digit - '0');
        }
        return decimal;
    }

    // Choose which conversion method to use
    public static int convert(String number, char base) {
        if (base == 'b') return binaryToDecimal(number);
        else if (base == 'o') return octalToDecimal(number);
        else if (base == 'h') return hexToDecimal(number);
        else if (base == 'd') return decimalToDecimal(number);
        else throw new IllegalArgumentException("Unknown base: " + base);
    }

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        try {
            System.out.print("Enter number1: ");
            String num1 = scanner.next();

            System.out.print("Enter base for number1 (d/b/o/h): ");
            char base1 = scanner.next().charAt(0);

            System.out.print("Enter number2: ");
            String num2 = scanner.next();

            System.out.print("Enter base for number2 (d/b/o/h): ");
            char base2 = scanner.next().charAt(0);

            int dec1 = convert(num1, base1);
            int dec2 = convert(num2, base2);

            System.out.println("Choose operation: (a/s/m/d)");
            char op = scanner.next().charAt(0);

            int result = 0;
            if (op == 'a') {
                result = dec1 + dec2;
            } else if (op == 's') {
                result = dec1 - dec2;
            } else if (op == 'm') {
                result = dec1 * dec2;
            } else if (op == 'd') {
                if (dec2 == 0) {
                    System.out.println("Error: Division by zero");
                    return;
                }
                result = dec1 / dec2;
            } else {
                System.out.println("Invalid operation");
                return;
            }

            System.out.println("Result: " + result + " (base 10)");

        } catch (IllegalArgumentException e) {
            System.out.println("Error: " + e.getMessage());
        } finally {
            scanner.close();
        }
    }
}
