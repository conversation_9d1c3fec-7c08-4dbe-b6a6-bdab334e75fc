/**
 * Custom exception class for payroll system errors
 * Demonstrates exception handling implementation
 */
public class PayrollException extends Exception {
    private String errorCode;
    
    // Default constructor
    public PayrollException() {
        super("Payroll system error occurred");
        this.errorCode = "PAYROLL_ERROR";
    }
    
    // Constructor with message (Method Overloading)
    public PayrollException(String message) {
        super(message);
        this.errorCode = "PAYROLL_ERROR";
    }
    
    // Constructor with message and error code (Method Overloading)
    public PayrollException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    // Constructor with message and cause (Method Overloading)
    public PayrollException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "PAYROLL_ERROR";
    }
    
    // Accessor method
    public String getErrorCode() {
        return errorCode;
    }
    
    // Mutator method
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    @Override
    public String toString() {
        return "PayrollException [" + errorCode + "]: " + getMessage();
    }
}
